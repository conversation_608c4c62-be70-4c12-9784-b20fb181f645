<template>
  <div class="common-table-page">
    <div class="back"><a-button class="back-btn" @click="toBack">返回</a-button></div>
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="方案编码">
        <a-input v-model="queryParam.caseCode" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="方案名称">
        <a-input v-model="queryParam.caseName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
      </a-form-item>
      <a-form-item label="模型应用场景">
        <a-select allowClear v-model="queryParam.scene" placeholder="请选择" :options="sceneOptions"></a-select>
      </a-form-item>
      <!-- <a-form-item label="调度类型">
        <a-select
          allowClear
          v-model="queryParam.dispathType"
          placeholder="请选择"
          :options="dispatchTypeOptions"
        ></a-select>
      </a-form-item> -->

      <a-form-item label="发起人">
        <a-select
          allowClear
          v-model="queryParam.userId"
          placeholder="请选择"
          :options="createdUserOptions"
          show-search
          :filter-option="
            (input, option) => option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
          "
        ></a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="渠道水动力仿真模型"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <!-- <a-button @click="handleDitchConfigure">沟渠配置</a-button>
            <a-button @click="handleProjectConfigure">工程配置</a-button> -->
            <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="danger" v-if="isChecked" @click="handleDelete">
              <a-icon type="delete" />
              删除
            </a-button>
          </div>
        </VxeTable>
        <DitchConfigureModal
          v-if="showDitchConfigureModal"
          :fcstRangeOptions="fcstRangeOptions"
          ref="ditchConfigureModalRef"
          @ok="onOperationComplete"
          @close="showDitchConfigureModal = false"
        />
        <ProjectConfigureModal
          v-if="showProjectConfigureModal"
          ref="projectConfigureModalRef"
          @ok="onOperationComplete"
          @close="showProjectConfigureModal = false"
        />
        <AddModal
          v-if="showAddModal"
          :fcstRangeOptions="fcstRangeOptions"
          ref="addModalRef"
          @ok="onOperationComplete"
          @close="showAddModal = false"
        />

        <DetailModal
          v-if="showDetailModal"
          :sceneOptions="sceneOptions"
          :dispatchTypeOptions="dispatchTypeOptions"
          :fcstRangeOptions="fcstRangeOptions"
          ref="detailModalRef"
          @ok="onOperationComplete"
          @close="showDetailModal = false"
        />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import { getComUserList, getOptions } from '@/api/common'
  import { getChSimPage, deleteChSim, getInParameter } from './services.js'

  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'
  import DitchConfigureModal from './modules/DitchConfigureModal.vue'
  import ProjectConfigureModal from './modules/ProjectConfigureModal.vue'
  import AddModal from './modules/AddModal/index.vue'
  import DetailModal from './modules/DetailModal/index.vue'
  import { dispatchTypeOptions, simulateTypeOptions, modelStatusOptions, sceneOptions } from './config.js'

  export default {
    name: 'SimulationModel',
    components: {
      VxeTable,
      VxeTableForm,
      DitchConfigureModal,
      ProjectConfigureModal,
      AddModal,
      DetailModal,
    },
    data() {
      return {
        exportLoading: false,
        showDitchConfigureModal: false,
        showProjectConfigureModal: false,
        showAddModal: false,
        showDetailModal: false,
        dispatchTypeOptions,
        simulateTypeOptions,
        sceneOptions,
        modelStatusOptions,
        createdUserOptions: [],
        fcstRangeOptions: [],

        list: [],
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          caseCode: undefined,
          caseName: undefined,
          scene: undefined,
          // dispathType: undefined,
          userId: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '方案编码',
            field: 'caseCode',
            minWidth: 220,
            showOverflow: 'tooltip',
          },
          {
            title: '方案名称',
            field: 'caseName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },

          {
            title: '仿真时段',
            field: 'outerId',
            minWidth: 200,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return `${row.startTime} - ${row.endTime}`
              },
            },
          },
          {
            title: '仿真生成时间',
            field: 'saveTime',
            minWidth: 170,
            showOverflow: 'tooltip',
          },
          {
            title: '发起人',
            field: 'createdUserName',
            minWidth: 80,
          },
          {
            title: '操作',
            field: 'operate',
            width: 230,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetail(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleCopy(row)}>复制</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    computed: {},
    watch: {},
    created() {
      getComUserList({}).then(res => {
        this.createdUserOptions = res?.data.map(el => ({ label: el.name, value: el.userId }))
      })
      getOptions('fcstRange').then(res => {
        this.fcstRangeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
        console.log('set info', this.fcstRangeOptions)
      })
    },

    methods: {
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 查询列表 */
      getList() {
        this.showAddModal = false
        this.loading = true
        this.selectChange({ records: [] })
        getChSimPage(this.queryParam).then(response => {
          this.list = response.data.data
          // this.list = response.data?.data || []
          this.total = response.data?.data?.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          caseCode: undefined,
          caseName: undefined,
          scene: undefined,
          // dispathType: undefined,
          userId: undefined,
          pageNum: 1,
          sort: [],
        }
        this.handleQuery()
      },
      // 返回
      toBack() {
        this.$router.push('/schedule/simulation-model')
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.chSimId)
        this.names = valObj.records.map(item => item.caseName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      handleDitchConfigure() {
        this.showDitchConfigureModal = true
        this.$nextTick(() => this.$refs.ditchConfigureModalRef.handleShow())
      },
      handleProjectConfigure() {
        this.showProjectConfigureModal = true
        this.$nextTick(() => this.$refs.projectConfigureModalRef.handleShow())
      },
      /* 新增 */
      handleAdd() {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.addModalRef.handleShow())
      },
      /* 详情 */
      handleDetail(record) {
        this.showDetailModal = true
        this.$nextTick(() => this.$refs.detailModalRef.handleShow(record))
      },
      // 复制
      handleCopy(record) {
        this.showAddModal = true
        this.$nextTick(() => this.$refs.addModalRef.handleShow(record, 'copy'))
      },
      // 下载日志
      handleDownload() {},
      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const chSimIds = row.chSimId ? [row.chSimId] : this.ids
        const names = row.caseName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteChSim({ chSimIds: chSimIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
      },

      // 导出
      handleExport() {
        return
        this.exportLoading = true
        getChSimPage({ ...this.queryParam, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
          this.exportLoading = false

          const exportColumns = [
            {
              title: '终端编码',
              field: 'caseCode',
              minWidth: 100,
            },
            {
              title: '方案名称',
              field: 'caseName',
              minWidth: 140,
            },
            {
              title: '终端简称',
              field: 'caseNameAbbr',
              minWidth: 120,
            },
            {
              title: '终端类别',
              field: 'sourceType',
              minWidth: 100,
            },
            {
              title: '终端型号',
              field: 'terminalModelNumber',
              minWidth: 120,
            },
            {
              title: '终端状态',
              field: 'terminalStatusCode',
              minWidth: 120,
            },
            {
              title: '监测指标',
              field: 'indexCodes',
              minWidth: 180,
            },
            {
              title: '传输方式',
              field: 'transmissionCode',
              minWidth: 100,
            },
            {
              title: '终端备注',
              field: 'remark',
              minWidth: 120,
            },

            {
              title: '运维部门',
              field: 'operationDept',
              minWidth: 100,
            },
            {
              title: '卡有效期',
              field: 'iotCardExpire',
              minWidth: 100,
            },
            {
              title: '负责人',
              field: 'responsiblePerson',
              minWidth: 100,
            },
            {
              title: '物联网卡号',
              field: 'iotCardNo',
              minWidth: 100,
            },
            {
              title: '剩余有效期',
              field: 'remainingPeriodValidity',
              minWidth: 100,
            },
            {
              title: '终端启用日期',
              field: 'enabledTime',
              minWidth: 120,
            },
            {
              title: '运营商',
              field: 'iotCardIsp',
              minWidth: 120,
            },
            {
              title: '是否自动续费',
              field: 'isRenewal',
              minWidth: 120,
            },
            {
              title: '到期提醒',
              field: 'isNotice',
              minWidth: 120,
            },
            {
              title: '运维备注',
              field: 'remarkOps',
              minWidth: 120,
            },
          ]
          const data = (res.data?.data || []).map(row => ({
            ...row,
            districtCode: this.terminalTypeOptions.find(el => el.key == row.sourceType)?.value,
            indexCodes: row.indexCodes
              .map(item => {
                return this.monitoringIndexOptions.find(el => el.key == item)?.value
              })
              .join('、'),

            transmissionCode: transmissionCodeTypes[row.transmissionCode],
            terminalStatusCode: this.terminalStatusOptions.find(el => el.key == row.terminalStatusCode)?.value,

            isNotice: row.isNotice == 1 ? '是' : '否',
            isRenewal: row.isRenewal == 1 ? '是' : '否',
          }))

          excelExport(exportColumns, data, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .back {
    width: 100%;
    background: #fff;
    padding: 10px;
    display: flex; /* 启用flexbox布局 */
    justify-content: flex-end; /* 子元素在主轴（水平方向）上右对齐 */

    .back-btn {
      // top: 10px;
      right: 10px;
      z-index: 999;
    }
  }
</style>
