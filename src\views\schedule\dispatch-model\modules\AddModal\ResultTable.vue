<template>
  <VxeTable
    ref="vxeTableRef"
    :columns="columns"
    :tableData="$attrs.dataSource"
    size="small"
    :tablePage="false"
    :isShowTableHeader="false"
  ></VxeTable>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable/index.vue'

  export default {
    name: 'ResultTable',
    props: ['resultData'],
    components: {
      VxeTable,
    },
    data() {
      return {
        columns: [
          {
            title: '预报时间',
            field: 'tm',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '水位(m)',
            field: 'wlv',
            minWidth: 100,
            align: 'center',
          }, 
          {
            title: '库容(万m³)',
            field: 'storage',
            minWidth: 100,
            align: 'center',
          },
          {
            title: '时段雨量(mm)',
            field: 'rain',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '入库流量(m³/s)',
            field: 'inflow',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '入库水量(万m³)',
            field: 'inWater',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '供水流量(m³/s)',
            field: 'outflow',
            minWidth: 120,
            align: 'center',
          },
           {
            title: '供水量(万m³)',
            field: 'outWater',
            minWidth: 110,
            align: 'center',
          },
          {
            title: '泄洪流量(m³/s)',
            field: 'floodflow',
            minWidth: 120,
            align: 'center',
          },
          {
            title: '泄洪量(m³)',
            field: 'floodWater',
            minWidth: 110,
            align: 'center',
          },
        ],
      }
    },
    computed: {},
    created() {},
    methods: {},
  }
</script>

<style lang="less" scoped></style>
